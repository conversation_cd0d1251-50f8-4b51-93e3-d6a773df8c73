@import "tailwindcss";

:root {
  /* Theme Colors */
  --primary-50: #fff7ed;
  --primary-100: #ffedd5;
  --primary-200: #fed7aa;
  --primary-300: #fdba74;
  --primary-400: #fb923c;
  --primary-500: #f97316;
  --primary-600: #ea580c;
  --primary-700: #c2410c;
  --primary-800: #9a3412;
  --primary-900: #7c2d12;

  --secondary-50: #fefce8;
  --secondary-100: #fef9c3;
  --secondary-200: #fef08a;
  --secondary-300: #fde047;
  --secondary-400: #facc15;
  --secondary-500: #eab308;
  --secondary-600: #ca8a04;
  --secondary-700: #a16207;
  --secondary-800: #854d0e;
  --secondary-900: #713f12;

  --accent-50: #fdf4ff;
  --accent-100: #fae8ff;
  --accent-200: #f5d0fe;
  --accent-300: #f0abfc;
  --accent-400: #e879f9;
  --accent-500: #d946ef;
  --accent-600: #c026d3;
  --accent-700: #a21caf;
  --accent-800: #86198f;
  --accent-900: #701a75;

  /* Background Colors */
  --bg-primary: #000000;
  --bg-secondary: #000000;
  --bg-tertiary: #000000;
  --bg-glass: rgba(0, 0, 0, 0.8);
  --bg-glass-dark: rgba(0, 0, 0, 0.9);

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;
  --text-accent: var(--primary-300);

  /* Border Colors */
  --border-primary: rgba(255, 255, 255, 0.1);
  --border-secondary: rgba(255, 255, 255, 0.05);
  --border-accent: var(--primary-500);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #000000, #111111);
  --gradient-secondary: linear-gradient(135deg, #000000, #000000);
  --gradient-accent: linear-gradient(135deg, #000000, #111111);
  --gradient-header: linear-gradient(90deg, #000000, #000000, #000000);
  --gradient-orange-underline: linear-gradient(90deg, #fb923c, #f97316, #ea580c);
}

@theme inline {
  --color-background: var(--bg-primary);
  --color-foreground: var(--text-primary);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

html {
  background: #000000;
}

body {
  background: #000000;
  color: var(--text-primary);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom utility classes */
.bg-theme-primary { background-color: var(--bg-primary); }
.bg-theme-secondary { background-color: var(--bg-secondary); }
.bg-theme-tertiary { background-color: var(--bg-tertiary); }
.bg-theme-glass { background-color: var(--bg-glass); }

.text-theme-primary { color: var(--text-primary); }
.text-theme-secondary { color: var(--text-secondary); }
.text-theme-muted { color: var(--text-muted); }
.text-theme-accent { color: var(--text-accent); }

.border-theme-primary { border-color: var(--border-primary); }
.border-theme-secondary { border-color: var(--border-secondary); }
.border-theme-accent { border-color: var(--border-accent); }

.gradient-primary { background: var(--gradient-primary); }
.gradient-secondary { background: var(--gradient-secondary); }
.gradient-accent { background: var(--gradient-accent); }
.gradient-header { background: var(--gradient-header); }
.gradient-orange-underline { background: var(--gradient-orange-underline); }

/* Enhanced Header Animations */
@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(251, 146, 60, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(251, 146, 60, 0.6), 0 0 30px rgba(251, 146, 60, 0.4);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Header specific animations */
.header-slide-in {
  animation: slideInFromTop 0.6s ease-out;
}

.nav-item-fade-in {
  animation: fadeInUp 0.5s ease-out forwards;
  opacity: 0;
}

.nav-item-fade-in:nth-child(1) { animation-delay: 0.1s; }
.nav-item-fade-in:nth-child(2) { animation-delay: 0.2s; }
.nav-item-fade-in:nth-child(3) { animation-delay: 0.3s; }
.nav-item-fade-in:nth-child(4) { animation-delay: 0.4s; }
.nav-item-fade-in:nth-child(5) { animation-delay: 0.5s; }

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced hover effects */
.hover-glow:hover {
  animation: pulse-glow 2s infinite;
}

/* Backdrop blur enhancement */
.backdrop-blur-enhanced {
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
}
